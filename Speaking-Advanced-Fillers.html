<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Speaking - Advanced Discourse Markers for Band 7.0-8.0</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.7;
            color: #1a1a1a;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-family: 'Playfair Display', serif;
            font-size: 3.2em;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.4em;
            font-weight: 300;
            opacity: 0.95;
            max-width: 800px;
            margin: 0 auto;
        }

        .lesson-content {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 24px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.15), 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .navigation {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 25px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .nav-btn {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 15px;
            font-weight: 500;
            letter-spacing: 0.025em;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        }

        .step {
            display: none;
            padding: 50px 60px;
            animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step h2 {
            font-family: 'Playfair Display', serif;
            color: #1e293b;
            font-size: 2.8em;
            font-weight: 600;
            margin-bottom: 30px;
            border-bottom: 4px solid #8b5cf6;
            padding-bottom: 15px;
            letter-spacing: -0.02em;
        }

        .step h3 {
            font-family: 'Inter', sans-serif;
            color: #334155;
            font-size: 1.6em;
            font-weight: 600;
            margin: 30px 0 20px 0;
            letter-spacing: -0.01em;
        }

        .insight-box {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border: 2px solid #10b981;
            padding: 25px;
            margin: 25px 0;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
        }

        .insight-box h4 {
            color: #047857;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .critical-point {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            padding: 25px;
            margin: 25px 0;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.1);
        }

        .critical-point h4 {
            color: #92400e;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .example-box {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 2px solid #3b82f6;
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
        }

        .example-box h4 {
            color: #1d4ed8;
            margin-bottom: 20px;
            font-size: 1.4em;
            font-weight: 600;
        }

        .advanced-feature {
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
            border: 2px solid #ec4899;
            padding: 25px;
            margin: 25px 0;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.1);
        }

        .advanced-feature h4 {
            color: #be185d;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .filler-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .filler-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 25px;
            border-radius: 16px;
            border-left: 6px solid #8b5cf6;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .filler-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .filler-card h5 {
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .audio-example {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            padding: 25px;
            margin: 25px 0;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.1);
        }

        .audio-example h4 {
            color: #0369a1;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .progress-bar {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            height: 12px;
            border-radius: 8px;
            margin: 30px 0;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            background: linear-gradient(90deg, #8b5cf6, #10b981);
            height: 100%;
            width: 0%;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }

        ul, ol {
            margin-left: 25px;
            margin-bottom: 20px;
        }

        li {
            margin-bottom: 12px;
            font-size: 1.05em;
            line-height: 1.7;
        }

        .highlight {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
            color: #92400e;
        }

        .speaking-sample {
            background: #ffffff;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            font-family: 'Inter', sans-serif;
            font-size: 1.1em;
            line-height: 1.8;
            color: #374151;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            font-style: italic;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .step {
                padding: 30px 25px;
            }
            
            .header h1 {
                font-size: 2.4em;
            }
            
            .header p {
                font-size: 1.2em;
            }
            
            .navigation {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-btn {
                width: 250px;
                margin: 8px 0;
            }

            .filler-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 1200px) {
            .step {
                padding: 60px 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>IELTS Speaking Mastery</h1>
            <p>Advanced Discourse Markers & Sophisticated Fillers for Band 7.0-8.0</p>
        </div>

        <div class="lesson-content">
            <div class="navigation">
                <button class="nav-btn active" onclick="showStep(0)">Introduction</button>
                <button class="nav-btn" onclick="showStep(1)">Thinking Time</button>
                <button class="nav-btn" onclick="showStep(2)">Elaboration</button>
                <button class="nav-btn" onclick="showStep(3)">Clarification</button>
                <button class="nav-btn" onclick="showStep(4)">Transition</button>
                <button class="nav-btn" onclick="showStep(5)">Examples</button>
                <button class="nav-btn" onclick="showStep(6)">Practice</button>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <!-- Introduction Step -->
            <div class="step active" id="step0">
                <h2>🎯 Advanced Discourse Markers for IELTS Speaking</h2>

                <h3>The Strategic Use of Sophisticated Fillers</h3>
                <p>For C1 students targeting Band 7.0-8.0, sophisticated discourse markers serve as powerful tools that demonstrate linguistic competence while providing crucial thinking time. Unlike basic fillers that may sound hesitant, advanced discourse markers enhance fluency and showcase your command of natural, idiomatic English.</p>

                <div class="insight-box">
                    <h4>🎓 Why Advanced Fillers Matter for Band 7.0-8.0</h4>
                    <ul>
                        <li><strong>Fluency Enhancement:</strong> Maintain speech flow while organizing complex thoughts</li>
                        <li><strong>Linguistic Sophistication:</strong> Demonstrate advanced vocabulary and natural expression</li>
                        <li><strong>Cognitive Processing Time:</strong> Allow for sophisticated idea development</li>
                        <li><strong>Native-like Discourse:</strong> Mirror authentic English conversation patterns</li>
                        <li><strong>Confidence Building:</strong> Reduce anxiety through strategic pausing techniques</li>
                    </ul>
                </div>

                <h3>The Difference: Basic vs. Advanced Fillers</h3>
                <div class="example-box">
                    <h4>❌ Basic Fillers (Band 5.0-6.0)</h4>
                    <div class="speaking-sample">
                        "Um... well... I think... you know... like... it's kind of... how can I say... difficult to answer..."
                    </div>

                    <h4>✅ Advanced Discourse Markers (Band 7.0-8.0)</h4>
                    <div class="speaking-sample">
                        "That's quite a thought-provoking question... Let me consider this for a moment... From my perspective... What I find particularly interesting about this topic is..."
                    </div>
                </div>

                <div class="critical-point">
                    <h4>⚡ Key Principle: Substance Over Stalling</h4>
                    <p>Advanced fillers should <strong>add value</strong> to your response, not merely fill silence. They should signal sophisticated thinking, provide structure, and demonstrate your ability to engage with complex topics at an academic level.</p>
                </div>

                <h3>Categories of Advanced Discourse Markers</h3>
                <div class="filler-grid">
                    <div class="filler-card">
                        <h5>🤔 Thinking Time Markers</h5>
                        <p>Sophisticated ways to buy processing time while maintaining engagement</p>
                    </div>
                    <div class="filler-card">
                        <h5>📈 Elaboration Signals</h5>
                        <p>Markers that indicate you're developing complex ideas</p>
                    </div>
                    <div class="filler-card">
                        <h5>🔍 Clarification Devices</h5>
                        <p>Tools for refining and specifying your thoughts</p>
                    </div>
                    <div class="filler-card">
                        <h5>🔄 Transition Facilitators</h5>
                        <p>Smooth connectors between different aspects of your response</p>
                    </div>
                </div>

                <div class="advanced-feature">
                    <h4>🔬 Strategic Implementation</h4>
                    <p>Effective use of advanced fillers requires:</p>
                    <ul>
                        <li><strong>Natural Integration:</strong> Seamless incorporation into your speech patterns</li>
                        <li><strong>Contextual Appropriateness:</strong> Matching markers to the complexity of your ideas</li>
                        <li><strong>Varied Usage:</strong> Avoiding repetition through diverse marker selection</li>
                        <li><strong>Purposeful Application:</strong> Using markers to enhance, not mask, your message</li>
                    </ul>
                </div>
            </div>

            <!-- Step 1: Thinking Time -->
            <div class="step" id="step1">
                <h2>🤔 Sophisticated Thinking Time Markers</h2>

                <p>These advanced markers provide processing time while demonstrating intellectual engagement with the question. They signal that you're considering the complexity of the topic rather than simply struggling for words.</p>

                <div class="example-box">
                    <h4>🎯 Advanced Thinking Time Markers</h4>
                    <div class="filler-grid">
                        <div class="filler-card">
                            <h5>Intellectual Engagement</h5>
                            <ul>
                                <li>"That's quite a thought-provoking question..."</li>
                                <li>"Let me consider this for a moment..."</li>
                                <li>"That's an interesting point to reflect on..."</li>
                                <li>"I need to think about this carefully..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Complexity Acknowledgment</h5>
                            <ul>
                                <li>"That's quite a complex issue..."</li>
                                <li>"There are several dimensions to consider here..."</li>
                                <li>"This touches on a number of important aspects..."</li>
                                <li>"That's a multifaceted question..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Personal Reflection</h5>
                            <ul>
                                <li>"From my experience..."</li>
                                <li>"Drawing on my background..."</li>
                                <li>"Reflecting on this topic..."</li>
                                <li>"When I consider my own perspective..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Analytical Approach</h5>
                            <ul>
                                <li>"If I analyze this systematically..."</li>
                                <li>"Looking at this from different angles..."</li>
                                <li>"To approach this comprehensively..."</li>
                                <li>"Examining the various factors involved..."</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="audio-example">
                    <h4>🎙️ Demonstration: Part 3 Question Response</h4>
                    <p><strong>Question:</strong> "Do you think technology has made people more or less creative?"</p>
                    <div class="speaking-sample">
                        "That's quite a thought-provoking question, actually... <em>[pause]</em> Let me consider this for a moment because there are several dimensions to explore here... <em>[pause]</em> From my perspective, I think technology has had a rather paradoxical effect on creativity..."
                    </div>
                </div>

                <div class="insight-box">
                    <h4>💡 Strategic Benefits</h4>
                    <ul>
                        <li><strong>Demonstrates sophistication:</strong> Shows you understand the question's complexity</li>
                        <li><strong>Buys processing time:</strong> Allows for thoughtful response formulation</li>
                        <li><strong>Sets academic tone:</strong> Establishes intellectual engagement</li>
                        <li><strong>Shows confidence:</strong> Indicates comfort with complex topics</li>
                    </ul>
                </div>

                <div class="critical-point">
                    <h4>⚠️ Usage Guidelines</h4>
                    <ul>
                        <li><strong>Use sparingly:</strong> 1-2 per response maximum</li>
                        <li><strong>Follow with substance:</strong> Ensure your actual response justifies the buildup</li>
                        <li><strong>Match complexity:</strong> Use sophisticated markers for complex questions only</li>
                        <li><strong>Avoid overuse:</strong> Don't use thinking time markers for simple questions</li>
                    </ul>
                </div>
            </div>

            <!-- Step 2: Elaboration -->
            <div class="step" id="step2">
                <h2>📈 Advanced Elaboration Signals</h2>

                <p>These sophisticated markers signal that you're developing complex ideas and adding depth to your response. They demonstrate your ability to think analytically and provide comprehensive answers.</p>

                <div class="example-box">
                    <h4>🎯 Sophisticated Elaboration Markers</h4>
                    <div class="filler-grid">
                        <div class="filler-card">
                            <h5>Depth Development</h5>
                            <ul>
                                <li>"What I find particularly interesting is..."</li>
                                <li>"To elaborate on this point..."</li>
                                <li>"Going deeper into this issue..."</li>
                                <li>"To expand on what I just mentioned..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Analytical Extension</h5>
                            <ul>
                                <li>"From a broader perspective..."</li>
                                <li>"If we consider the implications..."</li>
                                <li>"Taking this analysis further..."</li>
                                <li>"Building on this foundation..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Nuanced Addition</h5>
                            <ul>
                                <li>"What's particularly noteworthy is..."</li>
                                <li>"An additional consideration is..."</li>
                                <li>"It's worth noting that..."</li>
                                <li>"What makes this especially relevant is..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Comprehensive Coverage</h5>
                            <ul>
                                <li>"To give you a complete picture..."</li>
                                <li>"Looking at this holistically..."</li>
                                <li>"To address all aspects of this..."</li>
                                <li>"Considering the full spectrum..."</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="audio-example">
                    <h4>🎙️ Demonstration: Elaboration in Action</h4>
                    <p><strong>Context:</strong> Discussing environmental protection</p>
                    <div class="speaking-sample">
                        "I believe individual actions are crucial for environmental protection... <em>[pause]</em> What I find particularly interesting about this topic is how small behavioral changes can create ripple effects... <em>[pause]</em> To elaborate on this point, when people adopt sustainable practices, they often influence their social circles..."
                    </div>
                </div>

                <div class="insight-box">
                    <h4>💡 Strategic Value</h4>
                    <ul>
                        <li><strong>Shows analytical thinking:</strong> Demonstrates ability to develop ideas systematically</li>
                        <li><strong>Enhances coherence:</strong> Creates logical flow between ideas</li>
                        <li><strong>Displays sophistication:</strong> Shows command of academic discourse</li>
                        <li><strong>Extends response time:</strong> Allows for comprehensive answer development</li>
                    </ul>
                </div>
            </div>

            <!-- Step 3: Clarification -->
            <div class="step" id="step3">
                <h2>🔍 Advanced Clarification Devices</h2>

                <p>These markers help you refine your thoughts, correct course, or specify your meaning with precision. They demonstrate intellectual honesty and sophisticated communication skills.</p>

                <div class="example-box">
                    <h4>🎯 Sophisticated Clarification Markers</h4>
                    <div class="filler-grid">
                        <div class="filler-card">
                            <h5>Precision Enhancement</h5>
                            <ul>
                                <li>"To be more precise..."</li>
                                <li>"What I mean to say is..."</li>
                                <li>"To put it more accurately..."</li>
                                <li>"Let me clarify that point..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Refinement Signals</h5>
                            <ul>
                                <li>"Actually, let me rephrase that..."</li>
                                <li>"To be more specific..."</li>
                                <li>"What I'm really trying to say is..."</li>
                                <li>"Perhaps a better way to express this is..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Qualification Markers</h5>
                            <ul>
                                <li>"I should qualify that statement..."</li>
                                <li>"To be fair..."</li>
                                <li>"In all honesty..."</li>
                                <li>"To give a balanced view..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Perspective Adjustment</h5>
                            <ul>
                                <li>"On second thought..."</li>
                                <li>"Actually, thinking about it more..."</li>
                                <li>"Let me approach this differently..."</li>
                                <li>"From another angle..."</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="audio-example">
                    <h4>🎙️ Demonstration: Clarification in Context</h4>
                    <p><strong>Context:</strong> Discussing work-life balance</p>
                    <div class="speaking-sample">
                        "I think modern technology has improved work-life balance... <em>[pause]</em> Actually, let me rephrase that because it's more nuanced... <em>[pause]</em> What I mean to say is that technology has the potential to improve balance, but it depends entirely on how we use it..."
                    </div>
                </div>

                <div class="critical-point">
                    <h4>⚡ Intellectual Honesty</h4>
                    <p>Using clarification markers demonstrates <strong>intellectual maturity</strong> - the ability to recognize when your initial statement needs refinement. This shows sophisticated thinking rather than weakness.</p>
                </div>
            </div>

            <!-- Step 4: Transition -->
            <div class="step" id="step4">
                <h2>🔄 Sophisticated Transition Facilitators</h2>

                <p>These advanced markers create seamless connections between different aspects of your response, demonstrating your ability to organize complex ideas coherently.</p>

                <div class="example-box">
                    <h4>🎯 Advanced Transition Markers</h4>
                    <div class="filler-grid">
                        <div class="filler-card">
                            <h5>Perspective Shifts</h5>
                            <ul>
                                <li>"On the other hand..."</li>
                                <li>"From a different perspective..."</li>
                                <li>"Conversely..."</li>
                                <li>"Looking at the flip side..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Logical Progression</h5>
                            <ul>
                                <li>"Building on that idea..."</li>
                                <li>"This leads me to consider..."</li>
                                <li>"Following this line of thought..."</li>
                                <li>"Which brings me to another point..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Contrast Introduction</h5>
                            <ul>
                                <li>"However, it's important to note..."</li>
                                <li>"That said..."</li>
                                <li>"Nevertheless..."</li>
                                <li>"Despite this..."</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Synthesis Signals</h5>
                            <ul>
                                <li>"Bringing these ideas together..."</li>
                                <li>"To synthesize these points..."</li>
                                <li>"Ultimately..."</li>
                                <li>"When we consider all factors..."</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="audio-example">
                    <h4>🎙️ Demonstration: Smooth Transitions</h4>
                    <p><strong>Context:</strong> Discussing social media impact</p>
                    <div class="speaking-sample">
                        "Social media has certainly revolutionized communication... <em>[pause]</em> However, it's important to note that this revolution has come with significant drawbacks... <em>[pause]</em> Which brings me to another point about privacy concerns..."
                    </div>
                </div>

                <div class="insight-box">
                    <h4>💡 Coherence Enhancement</h4>
                    <ul>
                        <li><strong>Logical flow:</strong> Creates clear connections between ideas</li>
                        <li><strong>Sophisticated structure:</strong> Shows ability to organize complex thoughts</li>
                        <li><strong>Natural progression:</strong> Guides listener through your reasoning</li>
                        <li><strong>Academic discourse:</strong> Mirrors formal presentation style</li>
                    </ul>
                </div>
            </div>

            <!-- Step 5: Examples -->
            <div class="step" id="step5">
                <h2>💡 Complete Response Examples</h2>

                <p>Here are comprehensive examples showing how advanced discourse markers work together in complete IELTS Speaking responses.</p>

                <div class="example-box">
                    <h4>🎙️ Part 2: Complete Response</h4>
                    <p><strong>Topic:</strong> Describe a technological innovation that has changed your life</p>
                    <div class="speaking-sample">
                        "That's quite an interesting topic to reflect on... <em>[pause]</em> Let me consider this for a moment... <em>[pause]</em> I'd like to talk about smartphones, which have fundamentally transformed how I navigate daily life.
                        <br><br>
                        What I find particularly fascinating about this innovation is how it's essentially consolidated multiple devices into one... <em>[pause]</em> To elaborate on this point, my smartphone has replaced my camera, my music player, my GPS, and even my computer for many tasks.
                        <br><br>
                        However, it's important to note that this convenience has come with some drawbacks... <em>[pause]</em> What I mean to say is that while smartphones have increased efficiency, they've also created a certain dependency that I find concerning.
                        <br><br>
                        Ultimately, when I consider all factors, I believe smartphones represent both the best and most challenging aspects of modern innovation..."
                    </div>
                </div>

                <div class="example-box">
                    <h4>🎙️ Part 3: Complex Discussion</h4>
                    <p><strong>Question:</strong> How do you think artificial intelligence will affect employment in the future?</p>
                    <div class="speaking-sample">
                        "That's quite a multifaceted question that touches on several important dimensions... <em>[pause]</em> From my perspective, AI will have a paradoxical effect on employment.
                        <br><br>
                        What I find particularly noteworthy is how AI will simultaneously eliminate certain jobs while creating entirely new categories of work... <em>[pause]</em> To be more specific, while routine tasks may become automated, there will be increased demand for AI specialists, data analysts, and human-AI interaction designers.
                        <br><br>
                        However, I should qualify that statement by noting that this transition won't be seamless... <em>[pause]</em> Looking at this from a broader perspective, society will need to invest heavily in retraining programs to help workers adapt.
                        <br><br>
                        Bringing these ideas together, I believe the key challenge isn't whether AI will affect employment—it's how quickly we can adapt our educational and training systems to prepare for this transformation."
                    </div>
                </div>

                <div class="critical-point">
                    <h4>⚡ Integration Mastery</h4>
                    <p>Notice how these examples demonstrate <strong>natural integration</strong> of discourse markers without overwhelming the content. The markers enhance rather than dominate the response.</p>
                </div>
            </div>

            <!-- Step 6: Practice -->
            <div class="step" id="step6">
                <h2>🎯 Strategic Implementation & Practice</h2>

                <p>Mastering advanced discourse markers requires systematic practice and strategic implementation. Here's your roadmap to Band 7.0-8.0 fluency.</p>

                <div class="insight-box">
                    <h4>🎓 Implementation Strategy</h4>
                    <ol>
                        <li><strong>Start with one category:</strong> Master thinking time markers first</li>
                        <li><strong>Practice in isolation:</strong> Use each marker in simple sentences</li>
                        <li><strong>Integrate gradually:</strong> Combine different marker types</li>
                        <li><strong>Record yourself:</strong> Listen for natural flow and timing</li>
                        <li><strong>Seek feedback:</strong> Have teachers evaluate your usage</li>
                    </ol>
                </div>

                <div class="example-box">
                    <h4>📋 Daily Practice Routine</h4>
                    <div class="filler-grid">
                        <div class="filler-card">
                            <h5>Week 1-2: Foundation</h5>
                            <ul>
                                <li>Master 5 thinking time markers</li>
                                <li>Practice with simple topics</li>
                                <li>Focus on natural delivery</li>
                                <li>Record 2-minute responses daily</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Week 3-4: Expansion</h5>
                            <ul>
                                <li>Add elaboration signals</li>
                                <li>Practice with complex topics</li>
                                <li>Work on smooth transitions</li>
                                <li>Extend responses to 3-4 minutes</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Week 5-6: Integration</h5>
                            <ul>
                                <li>Combine all marker types</li>
                                <li>Practice Part 3 discussions</li>
                                <li>Focus on coherence</li>
                                <li>Simulate exam conditions</li>
                            </ul>
                        </div>
                        <div class="filler-card">
                            <h5>Week 7-8: Mastery</h5>
                            <ul>
                                <li>Natural, unconscious usage</li>
                                <li>Complex topic discussions</li>
                                <li>Varied marker selection</li>
                                <li>Full mock speaking tests</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="advanced-feature">
                    <h4>🔬 Self-Assessment Checklist</h4>
                    <ul>
                        <li><strong>Natural delivery:</strong> Do the markers sound conversational, not rehearsed?</li>
                        <li><strong>Appropriate timing:</strong> Are you using markers at logical points?</li>
                        <li><strong>Varied selection:</strong> Are you avoiding repetition of the same markers?</li>
                        <li><strong>Content enhancement:</strong> Do the markers add value to your response?</li>
                        <li><strong>Confident usage:</strong> Are you comfortable with the markers you're using?</li>
                    </ul>
                </div>

                <div class="critical-point">
                    <h4>⚠️ Common Pitfalls to Avoid</h4>
                    <ul>
                        <li><strong>Overuse:</strong> Too many markers can sound artificial</li>
                        <li><strong>Inappropriate complexity:</strong> Don't use sophisticated markers for simple ideas</li>
                        <li><strong>Mechanical delivery:</strong> Markers should sound natural, not memorized</li>
                        <li><strong>Content neglect:</strong> Don't let markers replace substantial ideas</li>
                        <li><strong>Inconsistent register:</strong> Match marker sophistication to your overall language level</li>
                    </ul>
                </div>

                <div class="audio-example">
                    <h4>🎯 Your Next Steps</h4>
                    <div class="speaking-sample">
                        "Remember: Advanced discourse markers are tools to enhance your natural speaking ability, not replace it. Start slowly, practice consistently, and focus on authentic integration. With dedicated practice, these markers will become an unconscious part of your sophisticated English expression, helping you achieve the Band 7.0-8.0 you're targeting."
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 7;

        function showStep(stepNumber) {
            // Hide all steps
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => {
                step.classList.remove('active');
            });

            // Show selected step
            document.getElementById(`step${stepNumber}`).classList.add('active');

            // Update navigation buttons
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach((btn, index) => {
                btn.classList.remove('active');
                if (index === stepNumber) {
                    btn.classList.add('active');
                }
            });

            // Update progress bar
            const progressFill = document.getElementById('progressFill');
            const progressPercentage = (stepNumber / (totalSteps - 1)) * 100;
            progressFill.style.width = progressPercentage + '%';

            currentStep = stepNumber;
        }

        // Initialize the lesson
        document.addEventListener('DOMContentLoaded', function() {
            showStep(0);
        });

        // Keyboard navigation for classroom presentation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' && currentStep > 0) {
                showStep(currentStep - 1);
            } else if (e.key === 'ArrowRight' && currentStep < totalSteps - 1) {
                showStep(currentStep + 1);
            }
        });
    </script>
</body>
</html>
